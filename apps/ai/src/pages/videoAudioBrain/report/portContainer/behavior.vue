<template>
  <div class="behavior">
    <template v-if="!aiClassBehaviorData">
      <div>
        <lineTitle title="学生个人行为" />
        <Line />
      </div>

      <div style="margin-top: 24px; width: 100%">
        <lineTitle title="学生群体行为" />
        <stuBehavior
          v-show="isStuAll"
          :isStuAll="isStuAll"
        />
        <div v-show="!isStuAll" style="position: relative; height: 180px">
          <ysEmpt />
        </div>
      </div>
      <div style="margin-top: 24px">
        <lineTitle title="教师行为" />
        <teachBehavior v-show="isTeachAll" :isTeachAll="isTeachAll" />
        <div v-show="!isTeachAll" style="position: relative; height: 180px">
          <ysEmpt />
        </div>
      </div>

      <div style="display: flex; justify-content: space-between;margin-top: 24px">
        <div style="flex: 0 0 49%">
          <lineTitle title="学生群体行为占比" />
          <Pie1 />
        </div>
        <div style="flex: 0 0 49%">
          <lineTitle title="教师行为占比" />
          <Pie2 />
        </div>
      </div>
    </template>

    <template v-else>
      <div>
        <lineTitle title="S-T分析" :tooltip="tooltip['st']" />
        <st />
        <!-- <div v-show="!aiClassBehaviorData.behavior" style="position: relative; height: 180px">
          <ysEmpt />
        </div> -->
      </div>

      <div style="margin-top: 64px">
        <lineTitle title="行为占比" />
        <behaviorRatio />
        <!-- <div v-show="!isTeachAll" style="position: relative; height: 180px">
          <ysEmpt />
        </div> -->
      </div>
    </template>

    <div style="margin-top: 24px;">
      <lineTitle title="课堂发言占比" />
      <speechRatio />
    </div>

    <div style="margin-top: 24px; padding-bottom: 24px">
      <lineTitle title="教师语速分析" :tooltip="tooltip['rate']" />
      <speechRate />
    </div>

    <div
      style="
        display: flex;
        justify-content: space-between;
        margin-top: 24px;
        padding-bottom: 24px;
      "
    >
      <div style="flex: 0 0 49%">
        <lineTitle title="口头禅" :tooltip="tooltip['ktc']" />
        <div style="margin-top: 10px; height: 168px">
          <ktc />
        </div>
      </div>
      <div style="flex: 0 0 49%">
        <lineTitle title="关键词" :tooltip="tooltip['wordCloud']" />
        <div style="margin-top: 10px; height: 168px">
          <wordCloud />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, Ref, inject } from "vue";
import lineTitle from "../../components/lineTitle.vue";
import speechRatio from "../charTabContainer/speechRatio/index.vue";
import speechRate from "../charTabContainer/speechRate/index.vue";
import ktc from "../charTabContainer/ktc/index.vue";
import wordCloud from "../charTabContainer/wordCloud/index.vue";
import Line from "../charTabContainer/behavior/line.vue";
import stuBehavior from "../charTabContainer/behavior/stu-behavior.vue";
import teachBehavior from "../charTabContainer/behavior/teach-behavior.vue";
import Pie1 from "../charTabContainer/behavior/pie1.vue";
import Pie2 from "../charTabContainer/behavior/pie2.vue";
import behaviorRatio from "./behaviorRatio.vue";
import st from "../charTabContainer/st/index.vue";
import { AiClassActionDataVo, AiClassBehaviorData } from "@/pages/videoAudioBrainOld/entity";
import { ysEmpt } from "@ys/ui";

const aiClassActionDataVo = inject<Ref<AiClassActionDataVo>>(
  "aiClassActionDataVo"
);
const aiClassBehaviorData = inject<Ref<AiClassBehaviorData>>("aiClassBehaviorData");

const isStuAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.listen.length > 0 ||
      aiClassActionDataVo?.value.recite.length > 0 ||
      aiClassActionDataVo?.value.reading.length > 0 ||
      aiClassActionDataVo?.value.writing.length > 0 ||
      aiClassActionDataVo?.value.discuss.length > 0 ||
      aiClassActionDataVo?.value.studentOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});

const isTeachAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.teach.length > 0 ||
      aiClassActionDataVo?.value.blackboardWriting.length > 0 ||
      aiClassActionDataVo?.value.makeInspectionTour.length > 0 ||
      aiClassActionDataVo?.value.onStageInteraction.length > 0 ||
      aiClassActionDataVo?.value.teacherOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});

interface Tooltip {
  [key: string]: {
    title: string;
    content: string;
  }
}

const tooltip = ref<Tooltip>({
  ktc: {
    title: "口头禅",
    content: "统计课堂中高频语气词和口头禅等，挖掘授课过程中识别语言盲区，提升表达效率，帮助教师逐步构建更精炼、更具针对性的教学话语体系。"
  },
  wordCloud: {
    title: "关键词",
    content: "统计课堂中高频语气词和口头禅等，挖掘授课过程中识别语言盲区，提升表达效率，帮助教师逐步构建更精炼、更具针对性的教学话语体系。"
  },
  rate: {
    title: "教师语速分析",
    content: "标准语速：200-500/分钟",
  },
  st: {
    title: "S-T分析",
    content: `
      <h4>ST曲线</h4>
      <div>通过S-T可以看出教学全过程中教师行为与学生行为随着时间是如何变化的：沿横轴方向的线段代表教师在讲话，沿纵轴方向的线段代表学生在讲话；当曲线偏向横轴时，表示教师活动占多数；偏向纵轴时，学生活动占多数；当某段曲线整体平行于45度线时， 表示在此段时间内教师和学生互动充分。</div>
      <h4>Rt-Ch教学类型</h4>
      <div>Rt是指教师行为的占有比例，此值越大说明教师行为越多。 Ch是教师行为与学生行为之间的转换次数，此值越大说明教师-学生行为的转换次数越多。</div>
      <div>四种教学模式的定义分别为：</div>
      <div>•练习型：Rt<=0.3，教师行为占有率较低时，以学生活动为主。</div>
      <div>•讲授型：Rt>=0.7，教师行为比例比较高，以教师讲授为主。</div>
      <div>•对话型：Ch>=0.4，师生转换次数比较高，师生之间互动充分 。</div>
      <div>•混合型：0.3&lt;Rt&lt;0.7，Ch&lt;0.4，教师行为占有率在一半附近时， 教师、学生均充分参与。</div>
    `
  },
  fourRader: {
    title: "“四何”分析",
    content: `
      <div>“四何问题” 是一种在教学中用于引导学生思考和促进知识建构的问题分类方式，包括 “是何”“为何”“如何”“若何” 四类问题。皆在从不同角度引导学生思考，形成一个逐步深入的问题链，能够帮助教师全面地设计教学问题，引导学生从对知识的简单认知，到深入理解、实际应用，再到创新思考，促进学生思维能力的发展和知识的有效建构。</div>
      <h4>l “是何” 问题</h4>
      <div>含义：主要是关于事实性知识的问题，旨在让学生了解事物的基本概念、定义、特征、分类等，是对 “是什么” 的询问。</div>
      <h4>l “为何” 问题</h4>
      <div>含义：侧重于探究事物的原因、原理和理由，即询问 “为什么”。通过这类问题，引导学生深入理解知识背后的因果关系和内在逻辑。</div>
      <h4>l “如何” 问题</h4>
      <div>含义：关注的是事物的方法、途径和程序，也就是 “怎么样” 的问题。它要求学生了解如何做某事或如何解决某个问题，强调对知识的应用和实践能力。</div>
      <h4>l “若何” 问题</h4>
      <div>含义：通常是假设某种情境或条件发生变化，询问学生 “将会怎样”。这类问题具有开放性和创新性，鼓励学生进行推测、想象和创造，培养学生的创新思维和批判性思维。</div>
    `
  },
  bloom: {
    title: "认知层级",
    content: `
      <div>基于布鲁姆教育目标分类学对问题进行的一种分析方法，旨在帮助教师更好地设计问题、促进学生的学习和评估教学效果。布鲁姆将认知领域的问题分为六个层次，从低到高依次为记忆、理解、应用、分析、评价和创造。</div>
      <div>l 记忆：主要涉及对事实、概念、原理等信息的简单再现和识别，问题通常较为直接，如 “五四运动的导火线是什么？”</div>
      <div>l 理解：要求学生能够把握知识的意义，能用自己的语言解释、转换信息，或依据所学内容进行推断，如 “解释一下什么是生态平衡？”</div>
      <div>l 应用：强调学生将所学知识运用到新情境中解决实际问题，如 “运用欧姆定律计算电路中的电流。”</div>
      <div>l 分析：需要学生将整体知识分解为部分，理解各部分之间的关系以及部分与整体的关系，如 “分析这首诗的结构和主题是如何相互关联的？”</div>
      <div>l 评价：学生要依据一定标准对所学内容或给定材料进行价值判断，如 “评价一下这位历史人物的功过是非。”</div>
      <div>l 创造：鼓励学生创造新的产品、观点或方法，将不同元素组合成新颖独特的整体，如 “设计一个环保型校园的规划方案</div>
    `
  }
});
</script>

<style lang="scss" scoped>
.behavior {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}
</style>
