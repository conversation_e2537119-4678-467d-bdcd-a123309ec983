<script lang="ts" setup>
import { ref, inject, Ref, computed, onUnmounted, watch } from "vue";
import ziMu from "./zimu/index.vue";
import behaviorAnalysis from "../portContainer/behaviorAnalysis.vue";
import blackboardAnalysis from "./blackboardAnalysis/index.vue";
import { AiCaptionTask, AiTaskGroupVo } from "./../../entity";
import { ysEmpt, ysIcon } from "@ys/ui";
import lineTitle from "../../components/lineTitle.vue";
import teachSummary from "./teachSummary/index.vue";
import Line from "./behavior/line.vue";
import stuBehavior from "./behavior/stu-behavior.vue";
import teachBehavior from "./behavior/teach-behavior.vue";
import Pie1 from "./behavior/pie1.vue";
import Pie2 from "./behavior/pie2.vue";
import { AiClassActionDataVo } from "@/pages/videoAudioBrainOld/entity";
import picImg from "../portContainer/pic.png";
import { emitter } from "@/utils/mitt";

const aiTaskGroupVo = inject<Ref<AiTaskGroupVo>>("aiTaskGroupVo");
const aiCaptionTask = inject<Ref<AiCaptionTask>>("aiCaptionTask");
const aiClassActionDataVo = inject<Ref<AiClassActionDataVo>>(
  "aiClassActionDataVo"
);
const classroomType = inject<Ref<number>>("classroomType");
const aiAuth = inject<Ref<number>>("aiAuth");
const currentMenu = inject<Ref<number>>("currentMenu");
const roleChats = inject<Ref<any>>("roleChats");

const tabList1 = ["学生行为", "教师行为", "行为占比"];
const tabList2 = ["布鲁姆分析", "“四何”分析"];
const active1 = ref(0);

const isStuAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.listen.length > 0 ||
      aiClassActionDataVo?.value.recite.length > 0 ||
      aiClassActionDataVo?.value.reading.length > 0 ||
      aiClassActionDataVo?.value.writing.length > 0 ||
      aiClassActionDataVo?.value.discuss.length > 0 ||
      aiClassActionDataVo?.value.studentOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});

const isTeachAll = computed(() => {
  if (
    aiClassActionDataVo?.value &&
    (aiClassActionDataVo?.value.teach.length > 0 ||
      aiClassActionDataVo?.value.blackboardWriting.length > 0 ||
      aiClassActionDataVo?.value.makeInspectionTour.length > 0 ||
      aiClassActionDataVo?.value.onStageInteraction.length > 0 ||
      aiClassActionDataVo?.value.teacherOther.length > 0)
  ) {
    return true;
  } else {
    return false;
  }
});

const handleTabClick = (index: number) => {
  active1.value = index;
};

const current4Active = ref(18);

emitter.on("on-change-current4-active", (data: any) => {
  current4Active.value = data;
});

watch(
  () => currentMenu?.value,
  (newV) => {
    if (newV) {
      active1.value = 0;
    }
  },
  {
    immediate: true,
  }
);

onUnmounted(() => {
  emitter.off("on-change-current4-active");
});

const columns27 = ref([
  {
    title: "问题",
    dataIndex: "问题",
    key: "问题",
    width: 400,
  },
  {
    title: "“四何”分析",
    dataIndex: "四何",
    key: "“四何”分析",
    align: "center",
    width: 150
  },
  {
    title: "认知层级",
    dataIndex: "布鲁姆",
    key: "认知层级",
    align: "center",
    width: 150
  },
]);

const tableData27 = computed(() => {
  const list = roleChats?.value[3];
  const find27 = list.find((item: any) => item.questionType == 27);
  if (find27) {
    const arr = JSON.parse(find27.answer);
    return arr;
  }
  return [];
})
const currentTableData27 = ref(tableData27.value);

const onClickRow = (record: any) => {
  return {
    onClick: () => {
      const time = record["时间"];
      if (time) {
        const startTime = time.split("-")[0];
        console.log("startTime", startTime);
        onJumpVideoCurrent(startTime);
      }
      // onJumpVideoCurrent(record)
    },
  };
};

const onJumpVideoCurrent = (time: any) => {
  const [hours, minutes, seconds] = time.split(":").map(Number);
  const s = hours * 3600 + minutes * 60 + seconds;
  const aiVideo = document.getElementById("ai-video") as HTMLVideoElement;
  const aiVideo1 = document.getElementById("ai-video1") as HTMLVideoElement;
  if (aiVideo) {
    aiVideo.currentTime = s;
  }
  if (aiVideo1) {
    aiVideo1.currentTime = s;
  }
};

const bindClass27Column = (record: any) => {
  const levelClassMap: { [key: string]: string } = {
    创造: "chuangzao",
    评价: "pingjia",
    应用: "yingyong",
    理解: "lijie",
    记忆: "jiyi",
    分析: "fenxi",
  };
  return levelClassMap[record["布鲁姆"]] || "hidden";
};

const bloomValue = ref<string | undefined>(undefined);
const fourRaderValue = ref<string | undefined>(undefined);
const bloomOptions = ref<string[]>(['记忆', '理解', '应用', '分析', '评价', '创造']);
const fourRaderOptions = ref<string[]>(["是何", "为何", "如何", "若何"]);

watch(
  [() => bloomValue.value, () => fourRaderValue.value],
  ([newBloomValue, newFourRaderValue]) => {
    currentTableData27.value = tableData27.value;

    if (newBloomValue || newFourRaderValue) {
      currentTableData27.value = currentTableData27.value.filter((item: any) => {
        const bloomMatch = newBloomValue ? item["布鲁姆"] === newBloomValue : true;
        const fourRaderMatch = newFourRaderValue ? item["四何"] === newFourRaderValue : true;

        return bloomMatch && fourRaderMatch;
      });
    }
  }
);

interface Tooltip {
  [key: string]: {
    title: string;
    content: string;
  }
}

const tooltip = ref<Tooltip>({
  fourRader: {
    title: "“四何”分析",
    content: `
      <div>“四何问题” 是一种在教学中用于引导学生思考和促进知识建构的问题分类方式，包括 “是何”“为何”“如何”“若何” 四类问题。皆在从不同角度引导学生思考，形成一个逐步深入的问题链，能够帮助教师全面地设计教学问题，引导学生从对知识的简单认知，到深入理解、实际应用，再到创新思考，促进学生思维能力的发展和知识的有效建构。</div>
      <h4>l “是何” 问题</h4>
      <div>含义：主要是关于事实性知识的问题，旨在让学生了解事物的基本概念、定义、特征、分类等，是对 “是什么” 的询问。</div>
      <h4>l “为何” 问题</h4>
      <div>含义：侧重于探究事物的原因、原理和理由，即询问 “为什么”。通过这类问题，引导学生深入理解知识背后的因果关系和内在逻辑。</div>
      <h4>l “如何” 问题</h4>
      <div>含义：关注的是事物的方法、途径和程序，也就是 “怎么样” 的问题。它要求学生了解如何做某事或如何解决某个问题，强调对知识的应用和实践能力。</div>
      <h4>l “若何” 问题</h4>
      <div>含义：通常是假设某种情境或条件发生变化，询问学生 “将会怎样”。这类问题具有开放性和创新性，鼓励学生进行推测、想象和创造，培养学生的创新思维和批判性思维。</div>
    `
  },
  bloom: {
    title: "认知层级",
    content: `
      <div>基于布鲁姆教育目标分类学对问题进行的一种分析方法，旨在帮助教师更好地设计问题、促进学生的学习和评估教学效果。布鲁姆将认知领域的问题分为六个层次，从低到高依次为记忆、理解、应用、分析、评价和创造。</div>
      <div>l 记忆：主要涉及对事实、概念、原理等信息的简单再现和识别，问题通常较为直接，如 “五四运动的导火线是什么？”</div>
      <div>l 理解：要求学生能够把握知识的意义，能用自己的语言解释、转换信息，或依据所学内容进行推断，如 “解释一下什么是生态平衡？”</div>
      <div>l 应用：强调学生将所学知识运用到新情境中解决实际问题，如 “运用欧姆定律计算电路中的电流。”</div>
      <div>l 分析：需要学生将整体知识分解为部分，理解各部分之间的关系以及部分与整体的关系，如 “分析这首诗的结构和主题是如何相互关联的？”</div>
      <div>l 评价：学生要依据一定标准对所学内容或给定材料进行价值判断，如 “评价一下这位历史人物的功过是非。”</div>
      <div>l 创造：鼓励学生创造新的产品、观点或方法，将不同元素组合成新颖独特的整体，如 “设计一个环保型校园的规划方案</div>
    `
  }
});

emitter.on("bloom-data", (value: unknown) => {
  bloomValue.value = value as string;
})

emitter.on("four-rader-data", (value: unknown) => {
  fourRaderValue.value = value as string;
})

</script>

<template>
  <div class="chat-tab" :style="{ padding: currentMenu === 3 ? '0' : '16px 20px 32px' }">
    <div
      style="height: 100%"
      v-if="currentMenu == 1 && (classroomType == 1 || classroomType == 3)"
    >
      <lineTitle title="S-T分析" />
      <behaviorAnalysis />
    </div>

    <!-- 微格课堂 -->
    <div v-if="currentMenu == 1 && classroomType == 2">
      <lineTitle title="板书分析" />
      <blackboardAnalysis />
    </div>
    <div v-else-if="currentMenu == 2">
      <lineTitle title="行为分析" />
      <behaviorAnalysis />
    </div>
    <!-- <div v-else-if="currentMenu == 2 && aiTaskGroupVo?.taskType == 3">
      <div
        style="
          width: 100%;
          height: 400px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
        "
      >
        <img :src="picImg" width="200" height="124" />
        <p style="color: #8c8c8c; margin-top: 8px">
          电影模式暂不支持课堂行为分析
        </p>
      </div>
    </div> -->

    <div
      v-else-if="currentMenu == 3"
      style="position: relative; height: 100%"
    >
      <teachSummary v-if="1" />
      <ysEmpt v-else />
    </div>
    <div
      v-else-if="currentMenu == 5"
      style="position: relative; height: 100%"
    >
      <ziMu v-if="aiCaptionTask" />
      <ysEmpt v-else />
    </div>
    <div v-else-if="currentMenu == 4 && current4Active == 27">
      <div style="margin-bottom: 16px;">
        <a-select
          v-model:value="bloomValue"
          allowClear
          placeholder="认知层级-全部"
          style="width: 200px"
        >
          <a-select-option v-for="(item, index) in bloomOptions" :key="index" :value="item">{{ item }}</a-select-option>
        </a-select>

        <a-select
          v-model:value="fourRaderValue"
          allowClear
          placeholder="“四何分析”-全部"
          style="width: 200px;margin-left: 24px;"
        >
          <a-select-option v-for="(item, index) in fourRaderOptions" :key="index" :value="item">{{ item }}</a-select-option>
        </a-select>
      </div>

      <a-table
        class="bloom-table"
        :dataSource="currentTableData27"
        :columns="columns27"
        :pagination="false"
        bordered
        :customRow="onClickRow"
      >
        <template #headerCell="{ column }">
          <div style="display: flex; align-items: center;">
            {{ column.title }}
            <a-tooltip
              v-if="column.dataIndex === '四何'"
              placement="bottomLeft"
              color="white"
              :overlayStyle="{maxWidth: '520px'}"
            >
              <ys-icon class="iconfont" type="iconyuanxingwenhao" style="font-size: 16px;color: #999;line-height: 0;margin-left: 5px;" />
              <template #title>
                <h3>{{ tooltip['fourRader'].title }}</h3>
                <div v-html="tooltip['fourRader'].content" style="color: #262626;line-height: 1.5;"></div>
              </template>
            </a-tooltip>
            <a-tooltip
              v-if="column.dataIndex === '布鲁姆'"
              placement="bottomLeft"
              color="white"
              :overlayStyle="{maxWidth: '520px'}"
            >
              <ys-icon class="iconfont" type="iconyuanxingwenhao" style="font-size: 16px;color: #999;line-height: 0;margin-left: 5px;" />
              <template #title>
                <h3>{{ tooltip['bloom'].title }}</h3>
                <div v-html="tooltip['bloom'].content" style="color: #262626;line-height: 1.5;"></div>
              </template>
            </a-tooltip>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === '四何'">
            <div :style="{display: ['为何', '如何', '是何', '若何'].includes(record['四何']) ? 'block' : 'none'}">{{ record["四何"] }}</div>
          </template>
          <template v-if="column.dataIndex === '布鲁姆'">
            <div class="assessment-level" :class="bindClass27Column(record)">
              {{ record["布鲁姆"] }}
            </div>
          </template>
        </template>
      </a-table>
    </div>
    <div v-else>
      <ziMu v-if="aiCaptionTask" />
      <ysEmpt v-else />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.chat-tab {
  height: 468px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;

  margin-top: 20px;
  border-radius: 4px;
  border: 1px solid #e5e5e5;
  padding: 16px 20px 32px;
}
.tab-head {
  background: #f5f5f5;
  border-radius: 6px;
  height: 40px;
  padding: 4px;
  display: flex;
  align-items: center;

  .tab-item {
    cursor: pointer;
    /* height: 100%; */
    line-height: 32px;
    padding: 0 16px;
    border-radius: 6px;
  }
  .tab-item:hover {
    color: #007aff;
    background: #fff;
  }
  .tab-item.active {
    color: #007aff;
    background: #fff;
  }
}

.tab-content {
  flex: 1;
  height: 0;
}

.bloom-table {
  .ant-table-cell-row-hover {
    background: #e6f6ff !important;
  }
}
.assessment-level {
  width: 48px;
  height: 24px;
  color: #fff;
  text-align: center;
  margin: 0 auto;
  line-height: 24px;
  border-radius: 4px;
  &.chuangzao {
    background: #f1584e;
  }
  &.pingjia {
    background: #f48a2e;
  }
  &.yingyong {
    background: #59be7f;
  }
  &.lijie {
    background: #4ab4d0;
  }
  &.jiyi {
    background: #8f7bd3;
  }
  &.fenxi {
    background: #facd0b;
  }
  &.hidden {
    display: none;
  }
}
</style>
