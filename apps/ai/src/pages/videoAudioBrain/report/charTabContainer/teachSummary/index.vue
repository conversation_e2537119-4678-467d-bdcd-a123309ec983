<template>
  <div class="teach-summary">
    <div class="content">
      <div class="content-left">
        <span class="icon-box">
          <img src="@/assets/images/teach_summary_1.svg" alt="">
        </span>
      </div>
      <div class="content-right">111</div>
    </div>
  </div>
</template>

<script setup>
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  .content-left {
    width: 160px;
    .icon-box {
      width: 36px;
      height: 36px;
      background: #007aff;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 20px;
        height: 20px;
      }
    }
  }
  .content-right {
    flex: 1;
  }
}
</style>